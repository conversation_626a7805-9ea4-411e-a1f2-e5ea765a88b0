# 连接qdrant

# 从本地Qdrant 6334端口加载已有向量数据到index
from qdrant_client import QdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import StorageContext, VectorStoreIndex

# 连接到本地Qdrant gRPC端口6334
qdrant_client = QdrantClient(
    host="localhost",
    port=6334,  # gRPC端口，比6333 HTTP端口性能更好
    prefer_grpc=True,
    timeout=10
)

# 从已有集合创建向量存储
collection_name = "course_materials"  # 使用已存在的集合
vector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant_client)
storage_context = StorageContext.from_defaults(vector_store=vector_store)

# 从Qdrant向量存储创建index（不重新写入数据）
index = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)

print("✅ 已从Qdrant 6334端口加载向量数据到index")

# =============================
# 1) 基础导入与全局设置
# =============================
import os
from llama_index.core import Settings, VectorStoreIndex, PromptTemplate
from llama_index.core import SimpleDirectoryReader
from llama_index.core.node_parser import SentenceSplitter
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.chat_engine import SimpleChatEngine

# 替换成你自己的 OpenAI API Key
os.environ["OPENAI_API_KEY"] = "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE"
# 如需自定义网关（如代理或 Azure），取消下面注释并替换
os.environ["OPENAI_BASE_URL"] = "https://api.openai-proxy.org/v1"



# ➊ 配置全局设置（替代 ServiceContext）
Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0.1,
    api_base="https://api.openai-proxy.org/v1"
)
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)


print("✅ 已设置 LLM 和 Embedding。\n")

# =============================
#  共享的 ChatSummaryMemoryBuffer
# =============================
print("\n" + "🌟 创建共享 ChatSummaryMemoryBuffer (token_limit=1000) ...\n")

memory = ChatSummaryMemoryBuffer.from_defaults(
    token_limit = 1000, # 测试用1000，真实用4000，感觉根本用不完
    llm=Settings.llm,  # 用同一个 LLM 进行摘要
    chat_store=chat_store,
    chat_store_key="condense_plus_chat",
)

print(memory.summarize_prompt)


# =============================
# 两个 Chat Engine（共享 memory）
# =============================
print("🚀 A) condense_plus 引擎（会用到上面的向量索引）\n")
from llama_index.core.prompts import PromptTemplate

# "condense_question"用的提示词
new_condense_prompt = PromptTemplate(
    "你是一个RAG（检索增强生成）开发专家，你将根据用户和AI助手之前的{{聊天历史}}，把{{学生最新提出的问题}}，改写成一个详细完整具体的、携带必要上下文的问题。\n"
    "注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\n"
    "=== 聊天历史 ===\n"
    "{chat_history}\n\n"
    "=== 学生最新提出的问题 ===\n"
    "{question}\n\n"
    "=== 改写后的独立问题 ===\n"
)



# 3. 自定义 context_prompt（整合检索内容和用户问题的核心提示词）
custom_context_prompt = (
    "你是一个专业的AI助手🎓，请基于以下检索到的相关文档来回答用户问题。\n\n"
    "📚 **相关文档内容：**\n"
    "{context_str}\n\n"
    "🎯 **回答要求：**\n"
    "1. 严格基于上述文档内容进行回答\n"
    "2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'\n"
    "3. 回答要条理清晰，使用适当的emoji让内容更生动\n"
    "4. 请引用具体的文档内容来支撑你的回答\n\n"
    "💡 **请基于以上文档和之前的对话历史来回答用户的问题。**"
)

# 4. 创建 condense_plus_context 引擎（正确的配置方式）


condense_question_plus_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    condense_question_prompt=new_condense_prompt,
    context_prompt=custom_context_prompt,             # 配置上下文整合提示词
    memory=memory,
    verbose=True
)



print("💬 B) simple 引擎（不检索，只与LLM聊天，但同样共享 memory）\n")
simple_engine = SimpleChatEngine.from_defaults(
    llm=Settings.llm,
    memory=memory,
    system_prompt=(
        "你叫做‘文文学习助手’。请简洁、清晰、有条理地回应，使用很多的emoji。"
    ),
    verbose=True
)
print("✅ 两个引擎均已就绪，且共享同一份 ChatSummaryMemoryBuffer。\n")

# =============================
# 正确获取和更新 condense_plus_context 提示词的方法


# 2. 正确访问内部 query_engine（这是关键！）
print("=== 探索 condense_plus_context 引擎的内部结构 ===")
print("可用属性:", [attr for attr in dir(condense_question_plus_engine)])
print(condense_question_plus_engine._system_prompt)
print(condense_question_plus_engine._context_prompt_template)







# =============================
#  交互式对话终端 多聊几轮 看见 role': 'system' 就是压缩后的信息
# 这个inspect_memory_messages就没有定义呀
# =============================
def interactive_chat():
    """交互式聊天终端，支持切换对话模式"""
    
    print("\n" + "="*90)
    print("🎯 欢迎使用交互式聊天终端！")
    print("📋 可用命令：")
    print("   1 - 切换到 condense_question 模式（RAG检索）")
    print("   2 - 切换到 simple 模式（纯对话）")
    print("   /memory - 查看当前记忆状态")
    print("   /reset - 重置对话记忆")
    print("   /quit - 退出")
    print("="*90 + "\n")
    
    current_engine = condense_engine
    current_mode = "condense_question"
    
    while True:
        # 显示当前模式
        mode_display = "🔎 RAG检索" if current_mode == "condense_question" else "💬 纯对话"
        user_input = input(f"[{mode_display}] 👤 你: ").strip()
        
        if not user_input:
            continue
            
        # 处理命令
        if user_input == "1":
            current_engine = condense_engine
            current_mode = "condense_question"
            print("✅ 已切换到 condense_question 模式（RAG检索）\n")
            continue
        elif user_input == "2":
            current_engine = simple_engine
            current_mode = "simple"
            print("✅ 已切换到 simple 模式（纯对话）\n")
            continue
        elif user_input == "/memory":
            inspect_memory_messages(memory, f"当前记忆状态 - {current_mode} 模式")
            continue
        elif user_input == "/reset":
            memory.reset()
            print("🔄 记忆已重置\n")
            continue
        elif user_input == "/quit":
            print("👋 再见！")
            break
            
        # 处理用户问题
        try:
            print(f"\n🤖 {current_mode} 回答：")
            response = current_engine.chat(user_input)
            print(f"{response}\n")
            print("-" * 60)
        except Exception as e:
            print(f"❌ 出错了: {e}\n")

# 启动交互式聊天
interactive_chat()

# 直接输出redis_llama_chat_001键的完整值
import redis
import json

redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
values = redis_client.lrange("redis_llama_shortchat_try_history", 0, -1)

print("=== redis_llama_shortchat_001 完整内容 ===")
for i, value in enumerate(values):
    parsed = json.loads(value)
    print(f"[{i}] {parsed}")

# 输出memory完整信息
# 之前说的get_all可以获得完整的聊天记录，但实际上只能获得内存中的聊天记录
# 从redis观察4000token的一个会话记录实际有10kb左右还是抗的住
print("=== Memory 完整信息 ===")
print(f"Memory类型: {type(memory)}")
print(f"Token限制: {memory.token_limit}")
print(f"Chat Store: {memory.chat_store}")
print(f"Chat Store Key: {memory.chat_store_key}")
print(f"Tokenizer函数: {memory.tokenizer_fn}")
print(f"摘要提示词: {memory.summarize_prompt}")

print("\n=== Memory.get() 消息列表（传给LLM的最终消息）===")
messages = memory.get()
for i, msg in enumerate(messages):
    print(f"[{i}] Role: {msg.role}")
    print(f"    Content: {msg.content}")
    print(f"    完整对象: {msg}")
    print("-" * 60)

print("\n=== Memory.get_all() 所有原始消息 ===")
all_messages = memory.get_all()
for i, msg in enumerate(all_messages):
    print(f"[{i}] Role: {msg.role}")
    print(f"    Content: {msg.content}")
    print(f"    完整对象: {msg}")
    print("-" * 60)

print(f"\n=== 消息统计 ===")
print(f"get()消息数量: {len(messages)}")
print(f"get_all()消息数量: {len(all_messages)}")

# 设置动态过滤样例代码

# 动态过滤查询函数
def filtered_query(question, course_id=None, material_id=None):
    filters_list = []
    
    if course_id:
        filters_list.append(
            MetadataFilter(key="course_id", value=course_id, operator=FilterOperator.EQ)
        )
    
    if material_id:
        filters_list.append(
            MetadataFilter(key="course_material_id", value=material_id, operator=FilterOperator.EQ)
        )
    
    if filters_list:
        filters = MetadataFilters(filters=filters_list)
        query_engine = index.as_query_engine(similarity_top_k=3, filters=filters)
    else:
        query_engine = index.as_query_engine(similarity_top_k=3)
    
    return query_engine.query(question)

# 使用示例
print("=== 不同过滤模式测试 ===")
print("1. 无过滤:")
result1 = filtered_query("函数的核心概念")
print(f"结果: {result1}\n")

print("2. 按course_id过滤:")
result2 = filtered_query("函数的核心概念", course_id="course_01")
print(f"结果: {result2}\n")

print("3. 按material_id过滤:")
result3 = filtered_query("函数的核心概念", material_id="material_001")
print(f"结果: {result3}\n")

from llama_index.core.chat_engine.condense_question import DEFAULT_PROMPT

print("=== Condense 默认提示词 ===")
print(DEFAULT_PROMPT.get_template())

# 修改问题的压缩提示词

from llama_index.core.prompts import PromptTemplate
from llama_index.core.chat_engine import CondenseQuestionChatEngine

new_condense_prompt = PromptTemplate(
    "你是一个RAG（检索增强生成）开发专家，你将根据用户和AI助手之前的{{聊天历史}}，把{{学生最新提出的问题}}，改写成一个详细完整具体的、携带必要上下文的问题。\n"
    "注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\n"
    "=== 聊天历史 ===\n"
    "{chat_history}\n\n"
    "=== 学生最新提出的问题 ===\n"
    "{question}\n\n"
    "=== 改写后的独立问题 ===\n"
)

condense_engine = index.as_chat_engine(
    chat_mode="condense_question",
    condense_question_prompt=new_condense_prompt,
    memory=memory,
    verbose=True
)

# 通过 condense_engine 拿到底层 query_engine
qe = condense_engine._query_engine

# 获取所有提示词
qe_prompts = qe.get_prompts()

print("=== QueryEngine 可改的提示词及其默认内容 ===")
for k, tmpl in qe_prompts.items():
    print(f"\n>>> 提示词键: {k}")
    try:
        print(tmpl.get_template())  # PromptTemplate / BasePromptTemplate 支持这个方法
    except AttributeError:
        print("(该提示词对象不支持 get_template，可直接 print 查看)")
        print(tmpl)

# 更新text_qa_template

from llama_index.core.prompts import PromptTemplate

# 新的 QA 模板（可以是中文，也可以是中英混合）
new_text_qa_template = PromptTemplate(
    "每次回答都要先说：哈哈！\n"
    "下面是从知识库检索到的上下文信息：\n"
    "---------------------\n"
    "{context_str}\n"
    "---------------------\n"
    "请你严格基于以上上下文回答用户的问题，禁止使用你的先验知识。\n"
    "如果上下文无法回答，请直接说'我不知道'。\n\n"
    "用户问题: {query_str}\n"
    "答案:"
)

# 更新底层 QueryEngine 的 text_qa_template
qe.update_prompts({
    "response_synthesizer:text_qa_template": new_text_qa_template
})

print("✅ 已更新 text_qa_template 提示词！")

memory.reset()

# =============================
# 6) 多轮对话（共享记忆）
# =============================
# 查看 ChatSummaryMemoryBuffer 生成的完整消息列表
from rich.console import Console
from rich.panel import Panel

console = Console(width=120)

def inspect_memory_messages(memory, title="Memory 消息检查"):
    console.print(Panel(f"🔍 {title}", style="bold yellow", width=120))
    
    messages = memory.get()  # 获取最终传给 LLM 的消息
    
    for i, msg in enumerate(messages):
        role = msg.role.value if hasattr(msg.role, 'value') else str(msg.role)
        content = msg.content
        
        style = "red" if role == "system" else "cyan" if role == "user" else "green"
        console.print(f"[{i}] {role.upper()}: {content}", style=style)
        console.print("-" * 80)
        
print("\n" + "#"*90)
print("🎬 场景说明：Python 初学者教程多轮问答，展示共享记忆效果。\n" )

# 1) 用 condense_question（会检索）
print("🔎 [回合1 | condense_question] 用户：'Python 中如何定义函数？'\n")
r1 = condense_engine.chat("Python 中如何定义函数？请给我一个简明扼要的说明和示例。") 
print("🤖 回答：\n", r1, "\n")
inspect_memory_messages(memory, "Round 1 后的消息状态")

# 2) 用 simple（不检索，走闲聊/需求澄清，但仍然写入同一份记忆）
print("🗣️ [回合2 | simple] 用户：'顺便把刚才的要点用 3 条列一下～'\n")
r2 = simple_engine.chat("顺便把你刚才关于函数定义的回答用 3 条要点列一下，简短一点。") 
print("🤖 回答：\n", r2, "\n")
inspect_memory_messages(memory, "Round 2 后的消息状态")

# 3) 再用 condense_question（继续检索式对话，但会带着同一个 memory 的历史）
print("🔎 [回合3 | condense_question] 用户：'函数参数是怎么回事？'\n")
r3 = condense_engine.chat("请基于我们刚才讨论的函数基础，详细解释一下函数参数的概念。") 
print("🤖 回答：\n", r3, "\n")
inspect_memory_messages(memory, "Round 3 后的消息状态")

print("#"*90 + "\n")